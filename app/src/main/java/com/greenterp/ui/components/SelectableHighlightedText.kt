package com.greenterp.ui.components

import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.text.selection.SelectionContainer
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.sp
import com.greenterp.ProcessedText
import kotlinx.coroutines.delay

/**
 * 可选择的高亮文本组件，支持文本选择和划词翻译
 * 使用 SelectionContainer 的文本选择机制，避免坐标计算问题
 */
@Composable
fun SelectableHighlightedText(
    processedText: ProcessedText,
    modifier: Modifier = Modifier,
    fontSize: TextUnit = 16.sp,
    normalColor: Color = MaterialTheme.colorScheme.onSurface,
    highlightColor: Color = Color.Red,
    replaceHighlightColor: Color = Color.Black,
    onTextClick: ((Int) -> Unit)? = null,
    onTextSelected: ((String) -> Unit)? = null // 文本选择回调
) {
    val clipboardManager = LocalClipboardManager.current

    // 监听剪贴板变化来检测文本选择
    var lastClipboardText by remember { mutableStateOf("") }
    var isMonitoring by remember { mutableStateOf(false) }

    // 初始化剪贴板内容
    LaunchedEffect(Unit) {
        val initialClipboard = clipboardManager.getText()?.text ?: ""
        lastClipboardText = initialClipboard
    }

    // 监控剪贴板变化的协程
    LaunchedEffect(isMonitoring) {
        if (isMonitoring) {
            // 延迟一下让选择操作完成
            delay(200)

            val currentClipboard = clipboardManager.getText()?.text ?: ""

            // 如果剪贴板内容变化且新内容来自当前文本
            if (currentClipboard != lastClipboardText &&
                currentClipboard.isNotEmpty() &&
                currentClipboard.length > 1 && // 避免单个字符
                processedText.text.contains(currentClipboard)) {

                lastClipboardText = currentClipboard
                onTextSelected?.invoke(currentClipboard.trim())
            }

            isMonitoring = false
        }
    }

    SelectionContainer(
        modifier = modifier.pointerInput(Unit) {
            detectTapGestures(
                onPress = {
                    // 检测到按压时开始监控剪贴板
                    isMonitoring = true
                },
                onLongPress = {
                    // 长按时也开始监控
                    isMonitoring = true
                }
            )
        }
    ) {
        HighlightedText(
            processedText = processedText,
            fontSize = fontSize,
            normalColor = normalColor,
            highlightColor = highlightColor,
            replaceHighlightColor = replaceHighlightColor,
            onTextClick = onTextClick
        )
    }
}