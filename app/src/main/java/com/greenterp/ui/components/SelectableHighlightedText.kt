package com.greenterp.ui.components

import androidx.compose.foundation.text.selection.SelectionContainer
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.sp
import com.greenterp.ProcessedText

/**
 * 可选择的高亮文本组件，支持文本选择和划词翻译
 * 使用 SelectionContainer 的文本选择机制，避免坐标计算问题
 */
@Composable
fun SelectableHighlightedText(
    processedText: ProcessedText,
    modifier: Modifier = Modifier,
    fontSize: TextUnit = 16.sp,
    normalColor: Color = MaterialTheme.colorScheme.onSurface,
    highlightColor: Color = Color.Red,
    replaceHighlightColor: Color = Color.Black,
    onTextClick: ((Int) -> Unit)? = null,
    onTextSelected: ((String) -> Unit)? = null // 文本选择回调
) {
    val clipboardManager = LocalClipboardManager.current
    
    // 监听剪贴板变化来检测文本选择
    var lastClipboardText by remember { mutableStateOf("") }
    
    LaunchedEffect(Unit) {
        // 初始化剪贴板内容
        val initialClipboard = clipboardManager.getText()?.text ?: ""
        lastClipboardText = initialClipboard
    }
    
    // 定期检查剪贴板变化（这是一个简化的实现）
    LaunchedEffect(processedText.text) {
        kotlinx.coroutines.delay(100) // 短暂延迟
        val currentClipboard = clipboardManager.getText()?.text ?: ""
        
        // 如果剪贴板内容变化且新内容来自当前文本
        if (currentClipboard != lastClipboardText && 
            currentClipboard.isNotEmpty() && 
            processedText.text.contains(currentClipboard)) {
            
            lastClipboardText = currentClipboard
            onTextSelected?.invoke(currentClipboard)
        }
    }

    SelectionContainer {
        HighlightedText(
            processedText = processedText,
            modifier = modifier,
            fontSize = fontSize,
            normalColor = normalColor,
            highlightColor = highlightColor,
            replaceHighlightColor = replaceHighlightColor,
            onTextClick = onTextClick
        )
    }
}
